import { createRouter, createWebHistory, type RouteRecordRaw } from 'vue-router'
import { ElMessage } from 'element-plus'

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: {
      title: '登录',
      requiresAuth: false
    }
  },
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/layout/AdminLayout.vue'),
    meta: {
      title: '仪表盘',
      requiresAuth: true
    },
    children: [
      {
        path: '',
        component: () => import('@/views/Dashboard.vue')
      }
    ]
  },
  {
    path: '/users',
    name: 'Users',
    component: () => import('@/layout/AdminLayout.vue'),
    meta: {
      title: '用户管理',
      requiresAuth: true
    },
    children: [
      {
        path: '',
        component: () => import('@/views/Users.vue')
      }
    ]
  },
  {
    path: '/listings',
    name: 'Listings',
    component: () => import('@/layout/AdminLayout.vue'),
    meta: {
      title: '挂牌信息',
      requiresAuth: true
    },
    children: [
      {
        path: '',
        component: () => import('@/views/Listings.vue')
      }
    ]
  },
  {
    path: '/orders',
    name: 'Orders',
    component: () => import('@/layout/AdminLayout.vue'),
    meta: {
      title: '订单管理',
      requiresAuth: true
    },
    children: [
      {
        path: '',
        component: () => import('@/views/Orders.vue')
      }
    ]
  },
  {
    path: '/packages',
    name: 'Packages',
    component: () => import('@/layout/AdminLayout.vue'),
    meta: {
      title: '套餐管理',
      requiresAuth: true
    },
    children: [
      {
        path: '',
        component: () => import('@/views/Packages.vue')
      }
    ]
  },
  {
    path: '/settings',
    name: 'Settings',
    component: () => import('@/layout/AdminLayout.vue'),
    meta: {
      title: '系统设置',
      requiresAuth: true
    },
    children: [
      {
        path: '',
        component: () => import('@/views/Settings.vue')
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue'),
    meta: {
      title: '页面不存在'
    }
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  document.title = to.meta?.title ? `${to.meta.title} - 甩单系统管理后台` : '甩单系统管理后台'

  // 检查是否需要登录
  if (to.meta?.requiresAuth) {
    const token = localStorage.getItem('admin_token')
    if (!token) {
      ElMessage.warning('请先登录')
      next('/login')
      return
    }
  }

  // 如果已登录且访问登录页，重定向到仪表盘
  if (to.path === '/login') {
    const token = localStorage.getItem('admin_token')
    if (token) {
      next('/dashboard')
      return
    }
  }

  next()
})

export default router
