/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

#app {
  height: 100%;
}

/* 覆盖Element Plus默认样式 */
.el-menu {
  border-right: none;
}

.el-header {
  padding: 0;
  border-bottom: 1px solid #e6e6e6;
}

.el-aside {
  border-right: 1px solid #e6e6e6;
}

.el-main {
  padding: 20px;
  background-color: #f5f5f5;
}

/* 表格样式 */
.el-table {
  background-color: white;
  border-radius: 4px;
}

.el-table th {
  background-color: #fafafa;
}

/* 卡片样式 */
.el-card {
  margin-bottom: 20px;
}

/* 分页样式 */
.el-pagination {
  margin-top: 20px;
  text-align: right;
}

/* 搜索表单样式 */
.search-form {
  background: white;
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 4px;
}

.search-form .el-form-item {
  margin-bottom: 0;
}

/* 操作按钮样式 */
.action-buttons {
  margin-bottom: 20px;
}

.action-buttons .el-button {
  margin-right: 10px;
}

/* 状态标签样式 */
.status-tag {
  margin-right: 5px;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .el-aside {
    width: 200px !important;
  }
  
  .el-main {
    padding: 10px;
  }
  
  .search-form {
    padding: 15px;
  }
}
