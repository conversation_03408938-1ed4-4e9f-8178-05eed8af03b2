<template>
  <div class="packages-page">
    <el-card>
      <template #header>
        <span>套餐管理</span>
      </template>
      <div class="coming-soon">
        <el-empty description="套餐管理功能开发中...">
          <el-button type="primary">敬请期待</el-button>
        </el-empty>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
// 套餐管理页面 - 待开发
</script>

<style scoped>
.packages-page {
  padding: 20px;
}

.coming-soon {
  padding: 60px 0;
  text-align: center;
}
</style>
