import { api } from '@/utils/request'

// 管理员登录接口
export interface LoginParams {
  username: string
  password: string
}

export interface LoginResponse {
  admin: {
    id: number
    username: string
    created_at: string
    updated_at: string
  }
  token: string
}

export interface ApiResponse<T = any> {
  success: boolean
  message: string
  data: T
}

export interface PaginatedResponse<T = any> {
  success: boolean
  message: string
  data: T[]
  pagination: {
    page: number
    pageSize: number
    total: number
    totalPages: number
  }
}

// 管理员相关API
export const adminApi = {
  // 登录
  login: (params: LoginParams): Promise<ApiResponse<LoginResponse>> => {
    return api.post('/admin/login', params)
  },

  // 获取当前管理员信息
  getCurrentAdmin: (): Promise<ApiResponse> => {
    return api.get('/admin/me')
  }
}

// 用户管理API
export interface User {
  id: number
  openid: string
  nickname?: string
  avatar_url?: string
  phone_number?: string
  status: 'inactive' | 'active'
  publishing_credits: number
  inviter_id?: number
  created_at: string
  updated_at: string
}

export interface UserListParams {
  page?: number
  pageSize?: number
  status?: 'inactive' | 'active'
  search?: string
}

export const userApi = {
  // 获取用户列表
  getList: (params: UserListParams): Promise<PaginatedResponse<User>> => {
    return api.get('/admin/users', { params })
  },

  // 获取用户详情
  getDetail: (id: number): Promise<ApiResponse<User>> => {
    return api.get(`/admin/users/${id}`)
  },

  // 更新用户状态
  updateStatus: (id: number, status: 'inactive' | 'active'): Promise<ApiResponse> => {
    return api.put(`/admin/users/${id}/status`, { status })
  },

  // 更新用户发布额度
  updateCredits: (id: number, credits: number): Promise<ApiResponse> => {
    return api.put(`/admin/users/${id}/credits`, { credits })
  }
}

// 挂牌信息管理API
export interface Listing {
  id: number
  user_id: number
  listing_type: '公司' | '个体户' | '代账户'
  company_name: string
  status: '在售' | '已售' | '下架'
  price?: number
  is_negotiable: boolean
  registration_province?: string
  registration_city?: string
  establishment_date?: string
  registered_capital_range?: string
  paid_in_status?: string
  company_type?: string
  tax_status?: string
  bank_account_status?: string
  has_trademark: boolean
  has_patent: boolean
  has_software_copyright: boolean
  has_license_plate: boolean
  has_social_security: boolean
  shareholder_background?: string
  has_bidding_history: boolean
  description?: string
  expires_at?: string
  created_at: string
  updated_at: string
  user?: {
    nickname?: string
    phone_number?: string
  }
}

export interface ListingListParams {
  page?: number
  pageSize?: number
  status?: '在售' | '已售' | '下架' | ''
  listing_type?: '公司' | '个体户' | '代账户'
  search?: string
}

export const listingApi = {
  // 获取挂牌信息列表
  getList: (params: ListingListParams): Promise<PaginatedResponse<Listing>> => {
    return api.get('/admin/listings', { params })
  },

  // 获取挂牌信息详情
  getDetail: (id: number): Promise<ApiResponse<Listing>> => {
    return api.get(`/admin/listings/${id}`)
  },

  // 更新挂牌信息状态
  updateStatus: (id: number, status: '在售' | '已售' | '下架'): Promise<ApiResponse> => {
    return api.put(`/admin/listings/${id}/status`, { status })
  },

  // 删除挂牌信息
  delete: (id: number): Promise<ApiResponse> => {
    return api.delete(`/admin/listings/${id}`)
  }
}
