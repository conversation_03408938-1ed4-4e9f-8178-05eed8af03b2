const { pool } = require('../config/db');

/**
 * 插入测试数据脚本
 */
async function insertTestData() {
  try {
    console.log('🚀 开始插入测试数据...');

    const connection = await pool.getConnection();

    try {
      // 检查是否已有数据
      const [userRows] = await connection.execute('SELECT COUNT(*) as count FROM users');
      if (userRows[0].count > 0) {
        console.log('⚠️ 数据库已有用户数据，跳过插入');
      } else {
        // 插入测试用户
        console.log('📝 插入测试用户...');
        await connection.execute(`
          INSERT INTO users (openid, nickname, avatar_url, status, publishing_credits) 
          VALUES 
          ('test_openid_1', '测试用户1', 'https://example.com/avatar1.jpg', 'active', 5),
          ('test_openid_2', '测试用户2', 'https://example.com/avatar2.jpg', 'inactive', 0),
          ('test_openid_3', '活跃用户', 'https://example.com/avatar3.jpg', 'active', 10)
        `);
        console.log('✅ 测试用户插入完成');
      }

      // 检查套餐数据
      const [packageRows] = await connection.execute('SELECT COUNT(*) as count FROM packages');
      if (packageRows[0].count > 0) {
        console.log('⚠️ 数据库已有套餐数据，跳过插入');
      } else {
        // 插入测试套餐
        console.log('📝 插入测试套餐...');
        await connection.execute(`
          INSERT INTO packages (title, description, price, credits_amount, package_type, is_active, sort_order)
          VALUES 
          ('新手体验包', '新用户专享，1条发布机会', 0.00, 1, 'free', 1, 1),
          ('基础套餐', '适合个人用户，5条发布机会', 9.90, 5, 'paid', 1, 2),
          ('标准套餐', '适合小企业，20条发布机会', 29.90, 20, 'paid', 1, 3),
          ('专业套餐', '适合中大企业，50条发布机会', 59.90, 50, 'paid', 1, 4)
        `);
        console.log('✅ 测试套餐插入完成');
      }

      // 检查挂牌信息数据
      const [listingRows] = await connection.execute('SELECT COUNT(*) as count FROM listings');
      if (listingRows[0].count > 0) {
        console.log('⚠️ 数据库已有挂牌信息，跳过插入');
      } else {
        // 插入测试挂牌信息
        console.log('📝 插入测试挂牌信息...');
        await connection.execute(`
          INSERT INTO listings (
            user_id, listing_type, company_name, status, price, is_negotiable,
            registration_province, registration_city, establishment_date,
            registered_capital_range, paid_in_status, company_type, tax_status,
            bank_account_status, has_trademark, has_patent, has_software_copyright,
            has_license_plate, has_social_security, shareholder_background,
            has_bidding_history, description
          ) VALUES 
          (1, '公司', '北京科技有限公司', '在售', 50000.00, 1, '北京市', '朝阳区', '2020-01-15', '100-500万', '已实缴', '普通公司', '一般纳税人', '已开户', 1, 0, 1, 0, 1, '自然人', 1, '优质科技公司，业务稳定，适合接手经营。公司成立3年，有稳定的客户群体，技术团队完整。'),
          (1, '个体户', '张三餐饮店', '在售', 8000.00, 0, '上海市', '浦东新区', '2021-06-20', '10万以下', '已实缴', '不确定', '小规模', '未开户', 0, 0, 0, 0, 0, '自然人', 0, '位置优越的餐饮个体户，客流量稳定，月营业额约2万元。'),
          (3, '公司', '深圳贸易有限公司', '在售', 80000.00, 1, '广东省', '深圳市', '2019-03-10', '500-1000万', '已实缴', '普通公司', '一般纳税人', '已开户', 0, 1, 0, 1, 1, '自然人', 1, '专业贸易公司，主营进出口业务，有完整的供应链体系。'),
          (3, '代账户', '财务代理服务', '在售', 15000.00, 0, '江苏省', '南京市', '2022-01-01', '50-100万', '已实缴', '普通公司', '小规模', '已开户', 0, 0, 1, 0, 0, '自然人', 0, '专业财务代理公司，客户稳定，业务成熟。'),
          (1, '公司', '成都文化传媒公司', '已售', 35000.00, 0, '四川省', '成都市', '2021-08-15', '100-500万', '未实缴', '普通公司', '小规模', '已开户', 1, 0, 0, 0, 0, '自然人', 0, '文化传媒公司，主营广告设计和活动策划。')
        `);
        console.log('✅ 测试挂牌信息插入完成');
      }

      console.log('🎉 测试数据插入完成！');

      // 显示数据统计
      const [userCount] = await connection.execute('SELECT COUNT(*) as count FROM users');
      const [listingCount] = await connection.execute('SELECT COUNT(*) as count FROM listings');
      const [packageCount] = await connection.execute('SELECT COUNT(*) as count FROM packages');

      console.log('\n📊 数据库统计:');
      console.log(`👥 用户数量: ${userCount[0].count}`);
      console.log(`📋 挂牌信息数量: ${listingCount[0].count}`);
      console.log(`📦 套餐数量: ${packageCount[0].count}`);

    } finally {
      connection.release();
    }

  } catch (error) {
    console.error('❌ 插入测试数据失败:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  insertTestData()
    .then(() => {
      console.log('✅ 测试数据插入成功完成！');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 测试数据插入失败:', error);
      process.exit(1);
    });
}

module.exports = { insertTestData };
