<template>
  <div class="users-page">
    <!-- 搜索表单 -->
    <el-card class="search-form">
      <el-form :model="searchForm" inline>
        <el-form-item label="用户状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="未激活" value="inactive" />
            <el-option label="已激活" value="active" />
          </el-select>
        </el-form-item>

        <el-form-item label="搜索关键词">
          <el-input
            v-model="searchForm.search"
            placeholder="请输入昵称或手机号"
            clearable
            style="width: 200px"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 用户列表 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <span>用户列表</span>
          <div class="header-actions">
            <el-button @click="handleRefresh">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="userList"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />

        <el-table-column label="头像" width="80">
          <template #default="{ row }">
            <el-avatar
              :src="row.avatar_url"
              :size="40"
            >
              {{ row.nickname?.charAt(0) || '用' }}
            </el-avatar>
          </template>
        </el-table-column>

        <el-table-column prop="nickname" label="昵称" min-width="120">
          <template #default="{ row }">
            {{ row.nickname || '未设置' }}
          </template>
        </el-table-column>

        <el-table-column prop="phone_number" label="手机号" min-width="120">
          <template #default="{ row }">
            {{ row.phone_number || '未绑定' }}
          </template>
        </el-table-column>

        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag
              :type="row.status === 'active' ? 'success' : 'warning'"
              class="status-tag"
            >
              {{ row.status === 'active' ? '已激活' : '未激活' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="publishing_credits" label="剩余条数" width="100" />

        <el-table-column label="注册时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="220" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                type="primary"
                size="small"
                @click="handleViewDetail(row)"
              >
                查看详情
              </el-button>

              <el-button
                v-if="row.status === 'inactive'"
                type="success"
                size="small"
                @click="handleActivate(row)"
              >
                激活
              </el-button>

              <el-button
                type="warning"
                size="small"
                @click="handleEditCredits(row)"
              >
                修改额度
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>

    <!-- 用户详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="用户详情"
      width="600px"
    >
      <div v-if="currentUser" class="user-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="用户ID">
            {{ currentUser.id }}
          </el-descriptions-item>
          <el-descriptions-item label="OpenID">
            {{ currentUser.openid }}
          </el-descriptions-item>
          <el-descriptions-item label="昵称">
            {{ currentUser.nickname || '未设置' }}
          </el-descriptions-item>
          <el-descriptions-item label="手机号">
            {{ currentUser.phone_number || '未绑定' }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="currentUser.status === 'active' ? 'success' : 'warning'">
              {{ currentUser.status === 'active' ? '已激活' : '未激活' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="剩余条数">
            {{ currentUser.publishing_credits }}
          </el-descriptions-item>
          <el-descriptions-item label="邀请者ID">
            {{ currentUser.inviter_id || '无' }}
          </el-descriptions-item>
          <el-descriptions-item label="注册时间">
            {{ formatDate(currentUser.created_at) }}
          </el-descriptions-item>
        </el-descriptions>

        <div v-if="currentUser.avatar_url" class="user-avatar">
          <h4>用户头像</h4>
          <el-image
            :src="currentUser.avatar_url"
            style="width: 100px; height: 100px"
            fit="cover"
          />
        </div>
      </div>
    </el-dialog>

    <!-- 修改额度对话框 -->
    <el-dialog
      v-model="creditsDialogVisible"
      title="修改发布额度"
      width="400px"
    >
      <el-form :model="creditsForm" label-width="100px">
        <el-form-item label="当前额度">
          <span>{{ currentUser?.publishing_credits || 0 }} 条</span>
        </el-form-item>
        <el-form-item label="增加额度" required>
          <el-input-number
            v-model="creditsForm.credits"
            :min="1"
            :max="1000"
            placeholder="请输入要增加的条数"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="creditsDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleConfirmCredits">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'
import { userApi, type User, type UserListParams } from '@/api/admin'

// 响应式数据
const loading = ref(false)
const userList = ref<User[]>([])
const detailDialogVisible = ref(false)
const creditsDialogVisible = ref(false)
const currentUser = ref<User | null>(null)

// 搜索表单
const searchForm = reactive<UserListParams>({
  status: undefined,
  search: ''
})

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 修改额度表单
const creditsForm = reactive({
  credits: 1
})

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString()
}

// 获取用户列表
const getUserList = async () => {
  loading.value = true
  try {
    const params: UserListParams = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      ...searchForm
    }

    const response = await userApi.getList(params)
    userList.value = response.data
    pagination.total = response.pagination.total
  } catch (error) {
    console.error('获取用户列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  getUserList()
}

// 重置搜索
const handleReset = () => {
  searchForm.status = undefined
  searchForm.search = ''
  pagination.page = 1
  getUserList()
}

// 刷新
const handleRefresh = () => {
  getUserList()
}

// 查看详情
const handleViewDetail = async (user: User) => {
  try {
    const response = await userApi.getDetail(user.id)
    currentUser.value = response.data
    detailDialogVisible.value = true
  } catch (error) {
    console.error('获取用户详情失败:', error)
  }
}

// 激活用户
const handleActivate = async (user: User) => {
  try {
    await ElMessageBox.confirm(`确定要激活用户 "${user.nickname || user.id}" 吗？`, '确认激活', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await userApi.updateStatus(user.id, 'active')
    ElMessage.success('用户激活成功')
    getUserList()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('激活用户失败:', error)
    }
  }
}

// 修改额度
const handleEditCredits = (user: User) => {
  currentUser.value = user
  creditsForm.credits = 1
  creditsDialogVisible.value = true
}

// 确认修改额度
const handleConfirmCredits = async () => {
  if (!currentUser.value) return

  try {
    await userApi.updateCredits(currentUser.value.id, creditsForm.credits)
    ElMessage.success('额度修改成功')
    creditsDialogVisible.value = false
    getUserList()
  } catch (error) {
    console.error('修改额度失败:', error)
  }
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.page = 1
  getUserList()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.page = page
  getUserList()
}

// 初始化
onMounted(() => {
  getUserList()
})
</script>

<style scoped>
.users-page {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.user-detail {
  padding: 20px 0;
}

.user-avatar {
  margin-top: 20px;
  text-align: center;
}

.user-avatar h4 {
  margin-bottom: 10px;
  color: #303133;
}

.status-tag {
  margin-right: 5px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  align-items: center;
}

.action-buttons .el-button {
  margin: 0;
}

.el-pagination {
  margin-top: 20px;
  text-align: right;
}

@media (max-width: 768px) {
  .users-page {
    padding: 15px;
  }

  .el-table {
    font-size: 12px;
  }

  .el-pagination {
    text-align: center;
  }

  .action-buttons {
    gap: 4px;
  }

  .action-buttons .el-button {
    font-size: 11px;
    padding: 4px 8px;
  }
}
</style>
