const { query } = require('../config/db');

class User {
  constructor(data) {
    this.id = data.id;
    this.openid = data.openid;
    this.nickname = data.nickname;
    this.avatar_url = data.avatar_url;
    this.phone_number = data.phone_number;
    this.status = data.status;
    this.publishing_credits = data.publishing_credits;
    this.inviter_id = data.inviter_id;
    this.created_at = data.created_at;
    this.updated_at = data.updated_at;
  }

  /**
   * 根据openid查找用户
   * @param {string} openid 微信openid
   * @returns {Promise<User|null>}
   */
  static async findByOpenid(openid) {
    const sql = 'SELECT * FROM users WHERE openid = ?';
    const rows = await query(sql, [openid]);
    return rows.length > 0 ? new User(rows[0]) : null;
  }

  /**
   * 根据ID查找用户
   * @param {number} id 用户ID
   * @returns {Promise<User|null>}
   */
  static async findById(id) {
    const sql = 'SELECT * FROM users WHERE id = ?';
    const rows = await query(sql, [id]);
    return rows.length > 0 ? new User(rows[0]) : null;
  }

  /**
   * 创建新用户
   * @param {Object} userData 用户数据
   * @returns {Promise<User>}
   */
  static async create(userData) {
    const sql = `
      INSERT INTO users (openid, nickname, avatar_url, phone_number, status, publishing_credits, inviter_id)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `;
    const params = [
      userData.openid,
      userData.nickname || null,
      userData.avatar_url || null,
      userData.phone_number || null,
      userData.status || 'inactive',
      userData.publishing_credits || 0,
      userData.inviter_id || null
    ];
    
    const result = await query(sql, params);
    return await User.findById(result.insertId);
  }

  /**
   * 更新用户信息
   * @param {number} id 用户ID
   * @param {Object} updateData 更新数据
   * @returns {Promise<User>}
   */
  static async update(id, updateData) {
    const fields = [];
    const values = [];
    
    // 动态构建更新字段
    Object.keys(updateData).forEach(key => {
      if (updateData[key] !== undefined) {
        fields.push(`${key} = ?`);
        values.push(updateData[key]);
      }
    });
    
    if (fields.length === 0) {
      throw new Error('没有要更新的字段');
    }
    
    values.push(id);
    const sql = `UPDATE users SET ${fields.join(', ')} WHERE id = ?`;
    
    await query(sql, values);
    return await User.findById(id);
  }

  /**
   * 更新最后登录时间
   * @param {number} id 用户ID
   * @returns {Promise<void>}
   */
  static async updateLastLogin(id) {
    const sql = 'UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = ?';
    await query(sql, [id]);
  }

  /**
   * 增加发布额度
   * @param {number} id 用户ID
   * @param {number} credits 增加的额度
   * @returns {Promise<User>}
   */
  static async addCredits(id, credits) {
    const sql = 'UPDATE users SET publishing_credits = publishing_credits + ? WHERE id = ?';
    await query(sql, [credits, id]);
    return await User.findById(id);
  }

  /**
   * 减少发布额度
   * @param {number} id 用户ID
   * @param {number} credits 减少的额度
   * @returns {Promise<User>}
   */
  static async reduceCredits(id, credits = 1) {
    const sql = 'UPDATE users SET publishing_credits = publishing_credits - ? WHERE id = ? AND publishing_credits >= ?';
    const result = await query(sql, [credits, id, credits]);
    
    if (result.affectedRows === 0) {
      throw new Error('发布额度不足');
    }
    
    return await User.findById(id);
  }

  /**
   * 激活用户
   * @param {number} id 用户ID
   * @returns {Promise<User>}
   */
  static async activate(id) {
    return await User.update(id, { status: 'active' });
  }

  /**
   * 获取用户统计信息
   * @returns {Promise<Object>}
   */
  static async getStats() {
    const totalSql = 'SELECT COUNT(*) as total FROM users';
    const activeSql = 'SELECT COUNT(*) as active FROM users WHERE status = "active"';
    const todaySql = 'SELECT COUNT(*) as today FROM users WHERE DATE(created_at) = CURDATE()';

    const [totalResult, activeResult, todayResult] = await Promise.all([
      query(totalSql),
      query(activeSql),
      query(todaySql)
    ]);

    return {
      total: totalResult[0].total,
      active: activeResult[0].active,
      today: todayResult[0].today
    };
  }

  /**
   * 获取用户列表（分页）
   * @param {Object} options 查询选项
   * @returns {Promise<Object>}
   */
  static async getList(options = {}) {
    const {
      page = 1,
      pageSize = 10,
      status,
      search
    } = options;

    // 确保page和pageSize是数字类型
    const pageNum = parseInt(page);
    const pageSizeNum = parseInt(pageSize);
    const offset = (pageNum - 1) * pageSizeNum;

    // 构建WHERE条件
    const conditions = [];
    const params = [];

    if (status) {
      conditions.push('status = ?');
      params.push(status);
    }

    if (search) {
      conditions.push('(nickname LIKE ? OR phone_number LIKE ?)');
      params.push(`%${search}%`, `%${search}%`);
    }

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

    // 查询总数
    const countSql = `
      SELECT COUNT(*) as total
      FROM users
      ${whereClause}
    `;
    const countResult = await query(countSql, params);
    const total = countResult[0].total;

    // 查询列表数据
    const listSql = `
      SELECT *
      FROM users
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT ${pageSizeNum} OFFSET ${offset}
    `;

    const rows = await query(listSql, params);

    return {
      data: rows.map(row => new User(row)),
      pagination: {
        page: pageNum,
        pageSize: pageSizeNum,
        total,
        totalPages: Math.ceil(total / pageSizeNum)
      }
    };
  }

  /**
   * 转换为JSON对象（隐藏敏感信息）
   * @returns {Object}
   */
  toJSON() {
    return {
      id: this.id,
      nickname: this.nickname,
      avatar_url: this.avatar_url,
      phone_number: this.phone_number,
      status: this.status,
      publishing_credits: this.publishing_credits,
      created_at: this.created_at,
      updated_at: this.updated_at
    };
  }
}

module.exports = User;
