-- Active: 1752025475794@@127.0.0.1@3306@shuaiDanSystem
-- -------------------------------------------------------------
-- 小程序后端数据库表结构
-- 数据库字符集推荐: utf8mb4
-- 数据库排序规则推荐: utf8mb4_unicode_ci
-- -------------------------------------------------------------

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 1. 用户表 (Users)
-- 存储小程序的用户信息，包括激活状态和可发布条数
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `openid` VARCHAR(128) NOT NULL COMMENT '微信用户唯一标识 OpenID',
  `nickname` VARCHAR(255) NULL DEFAULT NULL COMMENT '用户昵称',
  `avatar_url` VARCHAR(512) NULL DEFAULT NULL COMMENT '用户头像URL',
  `phone_number` VARCHAR(20) NULL DEFAULT NULL COMMENT '用户手机号',
  `status` ENUM('inactive', 'active') NOT NULL DEFAULT 'inactive' COMMENT '账户状态 (inactive: 未激活, active: 已激活)',
  `publishing_credits` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '剩余可发布信息条数',
  `inviter_id` BIGINT UNSIGNED NULL DEFAULT NULL COMMENT '邀请者的用户ID，关联本表的id',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_openid` (`openid`),
  UNIQUE KEY `uk_phone_number` (`phone_number`),
  INDEX `idx_inviter_id` (`inviter_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- ----------------------------
-- 2. 挂牌信息表 (Listings)
-- 核心表，用于存储所有待出售的公司、个体户等信息
-- ----------------------------
DROP TABLE IF EXISTS `listings`;
CREATE TABLE `listings` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` BIGINT UNSIGNED NOT NULL COMMENT '发布者ID，关联users表',
  `listing_type` ENUM('公司', '个体户', '代账户') NOT NULL COMMENT '信息类型',
  `company_name` VARCHAR(255) NOT NULL COMMENT '公司/个体户名称',
  `status` ENUM('在售', '已售', '下架') NOT NULL DEFAULT '在售' COMMENT '挂牌状态',
  `price` DECIMAL(12, 2) NULL DEFAULT NULL COMMENT '售价，为NULL表示面议',
  `is_negotiable` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否面议 (1:是, 0:否)',
  `registration_province` VARCHAR(100) NULL DEFAULT NULL COMMENT '注册省份',
  `registration_city` VARCHAR(100) NULL DEFAULT NULL COMMENT '注册城市',
  `establishment_date` DATE NULL DEFAULT NULL COMMENT '成立日期',
  `registered_capital_range` ENUM('10万以下', '10-50万', '50-100万', '100-500万', '500-1000万', '1000万以上') NULL DEFAULT NULL COMMENT '注册资本范围',
  `paid_in_status` ENUM('已实缴', '未实缴', '不确定') NULL DEFAULT NULL COMMENT '实缴状态',
  `company_type` ENUM('普通公司', '国家局公司', '上市公司', '不确定') NULL DEFAULT NULL COMMENT '公司类型',
  `tax_status` ENUM('未登记', '小规模', '一般纳税人', '未开业', '不确定') NULL DEFAULT NULL COMMENT '税务情况',
  `bank_account_status` ENUM('已开户', '未开户', '不确定') NULL DEFAULT NULL COMMENT '银行账户状态',
  `has_trademark` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否有商标',
  `has_patent` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否有专利',
  `has_software_copyright` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否有软著',
  `has_license_plate` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否有车牌指标',
  `has_social_security` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否有社保缴纳记录',
  `shareholder_background` ENUM('自然人', '国央企', '外资', '不确定') NULL DEFAULT NULL COMMENT '股东背景',
  `has_bidding_history` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否有招投标业绩',
  `description` TEXT NULL COMMENT '其他详细描述',
  `expires_at` TIMESTAMP NULL DEFAULT NULL COMMENT '信息有效期',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发布时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_query` (`listing_type`, `status`, `registration_city`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='挂牌信息表';

-- ----------------------------
-- 3. 套餐/商品表 (Packages)
-- 定义所有可供用户领取或购买的套餐
-- ----------------------------
DROP TABLE IF EXISTS `packages`;
CREATE TABLE `packages` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `title` VARCHAR(255) NOT NULL COMMENT '套餐标题',
  `description` VARCHAR(512) NULL DEFAULT NULL COMMENT '套餐描述',
  `price` DECIMAL(10, 2) NOT NULL DEFAULT 0.00 COMMENT '价格',
  `credits_amount` INT UNSIGNED NOT NULL COMMENT '此套餐包含的发布条数',
  `package_type` ENUM('free', 'paid', 'promo') NOT NULL COMMENT '套餐类型 (free:免费, paid:付费, promo:促销)',
  `is_active` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否上架 (1:是, 0:否)',
  `sort_order` INT NOT NULL DEFAULT 0 COMMENT '显示排序，数字越小越靠前',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='套餐/商品表';

-- ----------------------------
-- 4. 订单表 (Orders)
-- 记录用户的每一次购买/领取套餐的行为
-- ----------------------------
DROP TABLE IF EXISTS `orders`;
CREATE TABLE `orders` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_number` VARCHAR(64) NOT NULL COMMENT '系统生成的唯一订单号',
  `user_id` BIGINT UNSIGNED NOT NULL COMMENT '购买用户ID',
  `package_id` INT UNSIGNED NOT NULL COMMENT '购买的套餐ID',
  `amount` DECIMAL(10, 2) NOT NULL COMMENT '订单金额',
  `status` ENUM('pending', 'paid', 'failed', 'completed', 'canceled') NOT NULL DEFAULT 'pending' COMMENT '订单状态',
  `payment_method` VARCHAR(50) NULL DEFAULT NULL COMMENT '支付方式',
  `transaction_id` VARCHAR(255) NULL DEFAULT NULL COMMENT '支付网关返回的交易号',
  `paid_at` TIMESTAMP NULL DEFAULT NULL COMMENT '支付完成时间',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '订单创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '订单更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_number` (`order_number`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单表';

-- ----------------------------
-- 5. 邀请记录表 (Invitations)
-- 记录用户间的邀请关系及奖励发放情况
-- ----------------------------
DROP TABLE IF EXISTS `invitations`;
CREATE TABLE `invitations` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `inviter_user_id` BIGINT UNSIGNED NOT NULL COMMENT '邀请人用户ID',
  `invitee_user_id` BIGINT UNSIGNED NOT NULL COMMENT '被邀请人用户ID',
  `reward_type` ENUM('activation', 'credits') NULL DEFAULT NULL COMMENT '奖励类型',
  `reward_value` INT NULL DEFAULT NULL COMMENT '奖励数量 (例如: 1条发布次数)',
  `status` ENUM('claimed', 'unclaimed') NOT NULL DEFAULT 'unclaimed' COMMENT '奖励领取状态',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '邀请关系创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_invitee_user_id` (`invitee_user_id`) COMMENT '一个用户只能被邀请一次',
  INDEX `idx_inviter_user_id` (`inviter_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邀请记录表';

-- ----------------------------
-- 6. 聊天群组表 (ChatGroups)
-- ----------------------------
DROP TABLE IF EXISTS `chat_groups`;
CREATE TABLE `chat_groups` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `group_name` VARCHAR(255) NOT NULL COMMENT '群名称',
  `group_avatar` VARCHAR(512) NULL DEFAULT NULL COMMENT '群头像URL',
  `description` TEXT NULL COMMENT '群公告或描述',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聊天群组表';

-- ----------------------------
-- 7. 群组成员表 (GroupMembers)
-- ----------------------------
DROP TABLE IF EXISTS `group_members`;
CREATE TABLE `group_members` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `group_id` INT UNSIGNED NOT NULL COMMENT '群组ID',
  `user_id` BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
  `role` ENUM('member', 'admin', 'owner') NOT NULL DEFAULT 'member' COMMENT '成员角色',
  `joined_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '加入时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_group_user` (`group_id`, `user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='群组成员表';

-- ----------------------------
-- 8. 聊天消息表 (Messages)
-- ----------------------------
DROP TABLE IF EXISTS `messages`;
CREATE TABLE `messages` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `group_id` INT UNSIGNED NOT NULL COMMENT '群组ID',
  `sender_id` BIGINT UNSIGNED NOT NULL COMMENT '发送者用户ID',
  `message_type` ENUM('text', 'image', 'system', 'demand_card') NOT NULL DEFAULT 'text' COMMENT '消息类型',
  `content` TEXT NOT NULL COMMENT '消息内容 (可存储JSON)',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发送时间',
  PRIMARY KEY (`id`),
  INDEX `idx_group_id` (`group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聊天消息表';

-- ----------------------------
-- 9. 管理员表 (Administrators)
-- ----------------------------
DROP TABLE IF EXISTS `administrators`;
CREATE TABLE `administrators` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `username` VARCHAR(50) NOT NULL COMMENT '管理员用户名',
  `password` VARCHAR(255) NOT NULL COMMENT '密码哈希值',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员表';

-- ----------------------------
-- 设置外键约束
-- ----------------------------
ALTER TABLE `users` ADD CONSTRAINT `fk_users_inviter_id` FOREIGN KEY (`inviter_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE `listings` ADD CONSTRAINT `fk_listings_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `orders` ADD CONSTRAINT `fk_orders_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `orders` ADD CONSTRAINT `fk_orders_package_id` FOREIGN KEY (`package_id`) REFERENCES `packages` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE `invitations` ADD CONSTRAINT `fk_invitations_inviter_id` FOREIGN KEY (`inviter_user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `invitations` ADD CONSTRAINT `fk_invitations_invitee_id` FOREIGN KEY (`invitee_user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `group_members` ADD CONSTRAINT `fk_gm_group_id` FOREIGN KEY (`group_id`) REFERENCES `chat_groups` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `group_members` ADD CONSTRAINT `fk_gm_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `messages` ADD CONSTRAINT `fk_messages_group_id` FOREIGN KEY (`group_id`) REFERENCES `chat_groups` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `messages` ADD CONSTRAINT `fk_messages_sender_id` FOREIGN KEY (`sender_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

SET FOREIGN_KEY_CHECKS = 1;