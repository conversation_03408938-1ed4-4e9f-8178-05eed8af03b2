<template>
  <div class="orders-page">
    <el-card>
      <template #header>
        <span>订单管理</span>
      </template>
      <div class="coming-soon">
        <el-empty description="订单管理功能开发中...">
          <el-button type="primary">敬请期待</el-button>
        </el-empty>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
// 订单管理页面 - 待开发
</script>

<style scoped>
.orders-page {
  padding: 20px;
}

.coming-soon {
  padding: 60px 0;
  text-align: center;
}
</style>
