const { query } = require('../config/db');
const bcrypt = require('bcrypt');

/**
 * 管理员模型类
 */
class Administrator {
  constructor(data) {
    this.id = data.id;
    this.username = data.username;
    this.password = data.password;
    this.created_at = data.created_at;
    this.updated_at = data.updated_at;
  }

  /**
   * 根据用户名查找管理员
   * @param {string} username 用户名
   * @returns {Promise<Administrator|null>}
   */
  static async findByUsername(username) {
    const sql = 'SELECT * FROM administrators WHERE username = ?';
    const rows = await query(sql, [username]);
    return rows.length > 0 ? new Administrator(rows[0]) : null;
  }

  /**
   * 根据ID查找管理员
   * @param {number} id 管理员ID
   * @returns {Promise<Administrator|null>}
   */
  static async findById(id) {
    const sql = 'SELECT * FROM administrators WHERE id = ?';
    const rows = await query(sql, [id]);
    return rows.length > 0 ? new Administrator(rows[0]) : null;
  }

  /**
   * 创建新管理员
   * @param {Object} data 管理员数据
   * @returns {Promise<Administrator>}
   */
  static async create(data) {
    const { username, password } = data;
    
    // 密码加密
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(password, saltRounds);
    
    const sql = `
      INSERT INTO administrators (username, password)
      VALUES (?, ?)
    `;
    
    const result = await query(sql, [username, hashedPassword]);
    return await Administrator.findById(result.insertId);
  }

  /**
   * 验证密码
   * @param {string} password 明文密码
   * @returns {Promise<boolean>}
   */
  async verifyPassword(password) {
    return await bcrypt.compare(password, this.password);
  }

  /**
   * 更新管理员信息
   * @param {number} id 管理员ID
   * @param {Object} data 更新数据
   * @returns {Promise<Administrator>}
   */
  static async update(id, data) {
    const updateFields = [];
    const updateValues = [];
    
    if (data.username) {
      updateFields.push('username = ?');
      updateValues.push(data.username);
    }
    
    if (data.password) {
      const saltRounds = 10;
      const hashedPassword = await bcrypt.hash(data.password, saltRounds);
      updateFields.push('password = ?');
      updateValues.push(hashedPassword);
    }
    
    if (updateFields.length === 0) {
      throw new Error('没有要更新的字段');
    }
    
    updateValues.push(id);
    
    const sql = `
      UPDATE administrators 
      SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `;
    
    await query(sql, updateValues);
    return await Administrator.findById(id);
  }

  /**
   * 删除管理员
   * @param {number} id 管理员ID
   * @returns {Promise<boolean>}
   */
  static async delete(id) {
    const sql = 'DELETE FROM administrators WHERE id = ?';
    const result = await query(sql, [id]);
    return result.affectedRows > 0;
  }

  /**
   * 获取管理员列表
   * @param {Object} options 查询选项
   * @returns {Promise<Object>}
   */
  static async getList(options = {}) {
    const { page = 1, pageSize = 10 } = options;
    
    const pageNum = parseInt(page);
    const pageSizeNum = parseInt(pageSize);
    const offset = (pageNum - 1) * pageSizeNum;
    
    // 查询总数
    const countSql = 'SELECT COUNT(*) as total FROM administrators';
    const countResult = await query(countSql);
    const total = countResult[0].total;
    
    // 查询列表数据
    const listSql = `
      SELECT id, username, created_at, updated_at
      FROM administrators
      ORDER BY created_at DESC
      LIMIT ${pageSizeNum} OFFSET ${offset}
    `;
    
    const rows = await query(listSql);
    
    return {
      data: rows.map(row => new Administrator(row)),
      pagination: {
        page: pageNum,
        pageSize: pageSizeNum,
        total,
        totalPages: Math.ceil(total / pageSizeNum)
      }
    };
  }

  /**
   * 转换为JSON格式（不包含密码）
   * @returns {Object}
   */
  toJSON() {
    return {
      id: this.id,
      username: this.username,
      created_at: this.created_at,
      updated_at: this.updated_at
    };
  }
}

module.exports = Administrator;
